import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/base/core/view_state.dart';
import 'package:flutter_kit/src/datasource/models/user_account_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/profile_repository.dart';
import 'package:flutter_kit/src/shared/components/dialogs/status_dialog.dart';

/// 菜单项模型
class ProfileMenuItem {
  final String title;
  final String icon;
  final String subtitle;

  ProfileMenuItem({
    required this.title,
    required this.icon,
    required this.subtitle,
  });
}


/// 我的页面逻辑
class ProfileLogic extends ViewStateLogic {
  final ProfileRepository repository;
  UserAccountEntity? _userAccount;
  List<ProfileMenuItem> _menuItems = [];
  BuildContext? _context;

  UserAccountEntity? get userAccount => _userAccount;
  List<ProfileMenuItem> get menuItems => _menuItems;

  // 便捷的状态访问方法
  bool get isLoading => viewState is ViewStateLoading;
  bool get hasError => viewState is ViewStateError;
  bool get hasData => viewState is ViewStateSuccess;


  ProfileLogic({required this.repository}) {
    // 初始化菜单项，不依赖用户数据
    _initMenuItems();
  }

  /// 设置上下文，用于显示弹窗
  void setContext(BuildContext context) {
    _context = context;
    // 设置上下文后立即加载数据
    loadData();
  }
  
  @override
  void loadData() {
    if (_context == null) return;

    // 显示加载弹窗
    StatusDialog.showLoading(
      context: _context!,
      message: '加载用户信息...',
      timeoutDuration: const Duration(seconds: 30),
    );

    // 使用 GetCurrentUser 接口获取用户信息
    sendRequest<UserAccountEntity>(
      repository.getCurrentUser(),
      bindViewState: false, // 不绑定视图状态，使用弹窗代替
      successCallback: (data) {
        // 隐藏加载弹窗
        StatusDialog.hide(context: _context!);

        _userAccount = data;
        _initMenuItems();
        notifyListeners(); // 手动通知更新
      },
      failCallback: () {
        // 隐藏加载弹窗并显示错误
        StatusDialog.hide(context: _context!);
        StatusDialog.showError(
          context: _context!,
          message: '加载用户信息失败，请重试',
        );
      },
    );
  }

  /// 初始化菜单项
  void _initMenuItems() {
    _menuItems = [
      ProfileMenuItem(
        title: '我的简历',
        icon: '📄',
        subtitle: '完善简历，获得更多机会',
      ),
      ProfileMenuItem(
        title: '求职意向',
        icon: '🎯',
        subtitle: '设置期望职位和薪资',
      ),
      ProfileMenuItem(
        title: '投递记录',
        icon: '📝',
        subtitle: '查看投递历史',
      ),
      ProfileMenuItem(
        title: '收藏职位',
        icon: '❤️',
        subtitle: '管理收藏的职位',
      ),
      ProfileMenuItem(
        title: '面试邀请',
        icon: '📅',
        subtitle: '查看面试安排',
      ),
      ProfileMenuItem(
        title: '设置',
        icon: '⚙️',
        subtitle: '账号与隐私设置',
      ),
      ProfileMenuItem(
        title: '帮助与反馈',
        icon: '❓',
        subtitle: '常见问题和意见反馈',
      ),
      ProfileMenuItem(
        title: '关于我们',
        icon: 'ℹ️',
        subtitle: '了解更多信息',
      ),
    ];
  }
  
  /// 刷新数据
  void refreshData() {
    if (_context != null) {
      loadData();
    }
  }
  
  /// 退出登录
  void logout() {
    // TODO: 实现退出登录逻辑
  }
}
