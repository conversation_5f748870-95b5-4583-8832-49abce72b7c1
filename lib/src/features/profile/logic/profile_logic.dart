import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/logic/view_state_logic.dart';
import 'package:flutter_kit/src/datasource/models/user_account_entity.dart';
import 'package:flutter_kit/src/datasource/repositories/profile_repository.dart';

/// 菜单项模型
class ProfileMenuItem {
  final String title;
  final String icon;
  final String subtitle;

  ProfileMenuItem({
    required this.title,
    required this.icon,
    required this.subtitle,
  });
}


/// 我的页面逻辑
class ProfileLogic extends ViewStateLogic {
  final ProfileRepository repository;
  UserAccountEntity? _userAccount;
  List<ProfileMenuItem> _menuItems = [];

  UserAccountEntity? get userAccount => _userAccount;
  List<ProfileMenuItem> get menuItems => _menuItems;

  ProfileLogic({required this.repository});
  
  @override
  void loadData() {
    // 使用 GetCurrentUser 接口获取用户信息
    sendRequest<UserAccountEntity>(
      repository.getCurrentUser(),
      successCallback: (data) {
        _userAccount = data;
        _initMenuItems();
      },
    );
  }

  /// 初始化菜单项
  void _initMenuItems() {
    _menuItems = [
      ProfileMenuItem(
        title: '我的简历',
        icon: '📄',
        subtitle: '完善简历，获得更多机会',
      ),
      ProfileMenuItem(
        title: '求职意向',
        icon: '🎯',
        subtitle: '设置期望职位和薪资',
      ),
      ProfileMenuItem(
        title: '投递记录',
        icon: '📝',
        subtitle: '查看投递历史',
      ),
      ProfileMenuItem(
        title: '收藏职位',
        icon: '❤️',
        subtitle: '管理收藏的职位',
      ),
      ProfileMenuItem(
        title: '面试邀请',
        icon: '📅',
        subtitle: '查看面试安排',
      ),
      ProfileMenuItem(
        title: '设置',
        icon: '⚙️',
        subtitle: '账号与隐私设置',
      ),
      ProfileMenuItem(
        title: '帮助与反馈',
        icon: '❓',
        subtitle: '常见问题和意见反馈',
      ),
      ProfileMenuItem(
        title: '关于我们',
        icon: 'ℹ️',
        subtitle: '了解更多信息',
      ),
    ];
  }
  
  /// 刷新数据
  void refreshData() {
    loadData();
  }
  
  /// 退出登录
  void logout() {
    // TODO: 实现退出登录逻辑
  }
}
